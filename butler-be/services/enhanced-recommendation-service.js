import Order from "../models/Order.js";
import User from "../models/User.js";
import Dish from "../models/Dish.js";
import { getUserPreferences } from "./personalization-service.js";

/**
 * Enhanced recommendation service with advanced personalization
 * Incorporates time-based preferences, seasonal trends, and contextual understanding
 */

/**
 * Get enhanced user preferences with time-based and contextual analysis
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID (optional)
 * @param {number} limit - Number of recent orders to analyze
 * @returns {Promise<Object>} - Enhanced user preferences
 */
export const getEnhancedUserPreferences = async (
  userId,
  outletId = null,
  limit = 30
) => {
  try {
    // Get base preferences
    const basePreferences = await getUserPreferences(userId, outletId, limit);

    // Build query for orders with more detailed analysis
    const orderQuery = { userId };
    if (outletId) {
      orderQuery.outletId = outletId;
    }

    // Get recent orders with more fields
    const recentOrders = await Order.find(orderQuery)
      .populate(
        "items.dishId",
        "name category cuisine tags price isVeg isSpecial isSeasonal"
      )
      .sort({ createdAt: -1 })
      .limit(limit);

    // Enhanced analysis
    const timeBasedPreferences = analyzeTimeBasedPreferences(recentOrders);
    const seasonalPreferences = analyzeSeasonalPreferences(recentOrders);
    const contextualPatterns = analyzeContextualPatterns(recentOrders);
    const behaviorPatterns = analyzeBehaviorPatterns(recentOrders);

    return {
      ...basePreferences,
      timeBasedPreferences,
      seasonalPreferences,
      contextualPatterns,
      behaviorPatterns,
      lastAnalyzed: new Date(),
    };
  } catch (error) {
    console.error("Error getting enhanced user preferences:", error);
    return await getUserPreferences(userId, outletId, limit);
  }
};

/**
 * Analyze time-based ordering preferences
 * @param {Array} orders - User's order history
 * @returns {Object} - Time-based preferences
 */
const analyzeTimeBasedPreferences = (orders) => {
  const timeSlots = {
    morning: { categories: {}, cuisines: {}, count: 0, avgAmount: 0 },
    afternoon: { categories: {}, cuisines: {}, count: 0, avgAmount: 0 },
    evening: { categories: {}, cuisines: {}, count: 0, avgAmount: 0 },
    night: { categories: {}, cuisines: {}, count: 0, avgAmount: 0 },
  };

  const dayOfWeek = {
    weekday: { categories: {}, cuisines: {}, count: 0 },
    weekend: { categories: {}, cuisines: {}, count: 0 },
  };

  orders.forEach((order) => {
    const orderDate = new Date(order.createdAt);
    const hour = orderDate.getHours();
    const day = orderDate.getDay();
    const timeSlot = getTimeSlot(hour);
    const dayType = day === 0 || day === 6 ? "weekend" : "weekday";

    timeSlots[timeSlot].count++;
    timeSlots[timeSlot].avgAmount +=
      order.finalAmount || order.totalAmount || 0;
    dayOfWeek[dayType].count++;

    order.items.forEach((item) => {
      if (!item.dishId) return;

      const dish = item.dishId;
      const categoryName = dish.category?.name || "Other";
      const cuisine = dish.cuisine || "Other";

      // Time slot preferences
      timeSlots[timeSlot].categories[categoryName] =
        (timeSlots[timeSlot].categories[categoryName] || 0) + item.quantity;
      timeSlots[timeSlot].cuisines[cuisine] =
        (timeSlots[timeSlot].cuisines[cuisine] || 0) + item.quantity;

      // Day of week preferences
      dayOfWeek[dayType].categories[categoryName] =
        (dayOfWeek[dayType].categories[categoryName] || 0) + item.quantity;
      dayOfWeek[dayType].cuisines[cuisine] =
        (dayOfWeek[dayType].cuisines[cuisine] || 0) + item.quantity;
    });
  });

  // Calculate averages
  Object.keys(timeSlots).forEach((slot) => {
    if (timeSlots[slot].count > 0) {
      timeSlots[slot].avgAmount =
        timeSlots[slot].avgAmount / timeSlots[slot].count;
    }
  });

  return { timeSlots, dayOfWeek };
};

/**
 * Analyze seasonal ordering preferences
 * @param {Array} orders - User's order history
 * @returns {Object} - Seasonal preferences
 */
const analyzeSeasonalPreferences = (orders) => {
  const seasons = {
    spring: { categories: {}, cuisines: {}, count: 0 },
    summer: { categories: {}, cuisines: {}, count: 0 },
    autumn: { categories: {}, cuisines: {}, count: 0 },
    winter: { categories: {}, cuisines: {}, count: 0 },
  };

  orders.forEach((order) => {
    const month = new Date(order.createdAt).getMonth();
    const season = getSeason(month);

    seasons[season].count++;

    order.items.forEach((item) => {
      if (!item.dishId) return;

      const dish = item.dishId;
      const categoryName = dish.category?.name || "Other";
      const cuisine = dish.cuisine || "Other";

      seasons[season].categories[categoryName] =
        (seasons[season].categories[categoryName] || 0) + item.quantity;
      seasons[season].cuisines[cuisine] =
        (seasons[season].cuisines[cuisine] || 0) + item.quantity;
    });
  });

  return seasons;
};

/**
 * Analyze contextual ordering patterns
 * @param {Array} orders - User's order history
 * @returns {Object} - Contextual patterns
 */
const analyzeContextualPatterns = (orders) => {
  const patterns = {
    orderSizePreference: { small: 0, medium: 0, large: 0 },
    specialDishAffinity: 0,
    seasonalDishAffinity: 0,
    priceFlexibility: 0,
    explorationTendency: 0,
  };

  let totalOrders = orders.length;
  let uniqueDishes = new Set();
  let totalSpecialDishes = 0;
  let totalSeasonalDishes = 0;
  let priceVariations = [];

  orders.forEach((order) => {
    const itemCount = order.items.reduce((sum, item) => sum + item.quantity, 0);
    const orderAmount = order.finalAmount || order.totalAmount || 0;

    // Order size classification
    if (itemCount <= 2) patterns.orderSizePreference.small++;
    else if (itemCount <= 5) patterns.orderSizePreference.medium++;
    else patterns.orderSizePreference.large++;

    priceVariations.push(orderAmount);

    order.items.forEach((item) => {
      if (!item.dishId) return;

      const dish = item.dishId;
      uniqueDishes.add(dish._id.toString());

      if (dish.isSpecial) totalSpecialDishes += item.quantity;
      if (dish.isSeasonal) totalSeasonalDishes += item.quantity;
    });
  });

  // Calculate metrics
  const totalItems = orders.reduce(
    (sum, order) =>
      sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0),
    0
  );

  patterns.specialDishAffinity =
    totalItems > 0 ? (totalSpecialDishes / totalItems) * 100 : 0;
  patterns.seasonalDishAffinity =
    totalItems > 0 ? (totalSeasonalDishes / totalItems) * 100 : 0;
  patterns.explorationTendency =
    totalOrders > 0 ? (uniqueDishes.size / totalOrders) * 100 : 0;

  // Price flexibility (coefficient of variation)
  if (priceVariations.length > 1) {
    const mean =
      priceVariations.reduce((sum, val) => sum + val, 0) /
      priceVariations.length;
    const variance =
      priceVariations.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
      priceVariations.length;
    patterns.priceFlexibility =
      mean > 0 ? (Math.sqrt(variance) / mean) * 100 : 0;
  }

  return patterns;
};

/**
 * Analyze user behavior patterns
 * @param {Array} orders - User's order history
 * @returns {Object} - Behavior patterns
 */
const analyzeBehaviorPatterns = (orders) => {
  const patterns = {
    orderFrequency: "low", // low, medium, high
    loyaltyScore: 0, // 0-100
    adventurousness: 0, // 0-100
    priceConsciousness: "medium", // low, medium, high
    lastOrderDays: 0,
  };

  if (orders.length === 0) return patterns;

  // Calculate order frequency
  const firstOrder = new Date(orders[orders.length - 1].createdAt);
  const lastOrder = new Date(orders[0].createdAt);
  const daysDiff = Math.max(
    1,
    (lastOrder - firstOrder) / (1000 * 60 * 60 * 24)
  );
  const ordersPerWeek = (orders.length / daysDiff) * 7;

  if (ordersPerWeek >= 2) patterns.orderFrequency = "high";
  else if (ordersPerWeek >= 0.5) patterns.orderFrequency = "medium";
  else patterns.orderFrequency = "low";

  // Calculate loyalty score (repeat orders)
  const dishCounts = {};
  orders.forEach((order) => {
    order.items.forEach((item) => {
      if (item.dishId) {
        const dishId = item.dishId._id.toString();
        dishCounts[dishId] = (dishCounts[dishId] || 0) + item.quantity;
      }
    });
  });

  const repeatOrders = Object.values(dishCounts).filter(
    (count) => count > 1
  ).length;
  const totalUniqueDishes = Object.keys(dishCounts).length;
  patterns.loyaltyScore =
    totalUniqueDishes > 0 ? (repeatOrders / totalUniqueDishes) * 100 : 0;

  // Calculate adventurousness (variety in orders)
  patterns.adventurousness = Math.min(
    100,
    (totalUniqueDishes / orders.length) * 50
  );

  // Calculate days since last order
  patterns.lastOrderDays = Math.floor(
    (new Date() - lastOrder) / (1000 * 60 * 60 * 24)
  );

  return patterns;
};

/**
 * Helper function to get time slot from hour
 * @param {number} hour - Hour of the day (0-23)
 * @returns {string} - Time slot
 */
const getTimeSlot = (hour) => {
  if (hour >= 6 && hour < 12) return "morning";
  if (hour >= 12 && hour < 17) return "afternoon";
  if (hour >= 17 && hour < 22) return "evening";
  return "night";
};

/**
 * Helper function to get season from month
 * @param {number} month - Month (0-11)
 * @returns {string} - Season
 */
const getSeason = (month) => {
  if (month >= 2 && month <= 4) return "spring";
  if (month >= 5 && month <= 7) return "summer";
  if (month >= 8 && month <= 10) return "autumn";
  return "winter";
};

/**
 * Get current context for recommendations
 * @returns {Object} - Current context
 */
export const getCurrentContext = () => {
  const now = new Date();
  return {
    timeSlot: getTimeSlot(now.getHours()),
    season: getSeason(now.getMonth()),
    dayOfWeek: now.getDay() === 0 || now.getDay() === 6 ? "weekend" : "weekday",
    hour: now.getHours(),
    month: now.getMonth(),
    isLunchTime: now.getHours() >= 11 && now.getHours() <= 14,
    isDinnerTime: now.getHours() >= 18 && now.getHours() <= 21,
  };
};

/**
 * Calculate enhanced personalization score for a dish
 * @param {Object} dish - Dish object
 * @param {Object} enhancedPreferences - Enhanced user preferences
 * @param {Object} currentContext - Current context (time, season, etc.)
 * @param {string} userQuery - User's current query/message
 * @returns {number} - Enhanced personalization score (0-1)
 */
export const calculateEnhancedPersonalizationScore = (
  dish,
  enhancedPreferences,
  currentContext,
  userQuery = ""
) => {
  let score = 0;
  const {
    preferences,
    dishFrequency,
    timeBasedPreferences,
    seasonalPreferences,
    contextualPatterns,
    behaviorPatterns,
  } = enhancedPreferences;

  // Base personalization score (40% weight)
  const baseScore = calculateBasePersonalizationScore(
    dish,
    enhancedPreferences
  );
  score += baseScore * 0.4;

  // Time-based preferences (20% weight)
  const timeScore = calculateTimeBasedScore(
    dish,
    timeBasedPreferences,
    currentContext
  );
  score += timeScore * 0.2;

  // Seasonal preferences (15% weight)
  const seasonalScore = calculateSeasonalScore(
    dish,
    seasonalPreferences,
    currentContext
  );
  score += seasonalScore * 0.15;

  // Contextual patterns (15% weight)
  const contextualScore = calculateContextualScore(
    dish,
    contextualPatterns,
    currentContext
  );
  score += contextualScore * 0.15;

  // Query relevance (10% weight)
  const queryScore = calculateQueryRelevanceScore(dish, userQuery);
  score += queryScore * 0.1;

  return Math.min(score, 1);
};

/**
 * Calculate base personalization score
 * @param {Object} dish - Dish object
 * @param {Object} preferences - User preferences
 * @returns {number} - Base score (0-1)
 */
const calculateBasePersonalizationScore = (dish, preferences) => {
  let score = 0;
  const { dishFrequency } = preferences;

  // Previously ordered dish (highest weight)
  const dishId = dish._id.toString();
  if (dishFrequency[dishId]) {
    const frequency = dishFrequency[dishId];
    score += Math.min(0.5, frequency * 0.1); // Cap at 0.5
  }

  // Category preference
  const dishCategory = dish.category?.name || dish.category;
  if (dishCategory && preferences.preferences.favoriteCategories.length > 0) {
    const categoryMatch = preferences.preferences.favoriteCategories.find(
      (cat) => cat.category === dishCategory
    );
    if (categoryMatch) {
      score += (categoryMatch.percentage / 100) * 0.3;
    }
  }

  // Cuisine preference
  if (dish.cuisine && preferences.preferences.favoriteCuisines.length > 0) {
    const cuisineMatch = preferences.preferences.favoriteCuisines.find(
      (cui) => cui.cuisine === dish.cuisine
    );
    if (cuisineMatch) {
      score += (cuisineMatch.percentage / 100) * 0.2;
    }
  }

  return Math.min(score, 1);
};

/**
 * Calculate time-based score
 * @param {Object} dish - Dish object
 * @param {Object} timeBasedPreferences - Time-based preferences
 * @param {Object} currentContext - Current context
 * @returns {number} - Time-based score (0-1)
 */
const calculateTimeBasedScore = (
  dish,
  timeBasedPreferences,
  currentContext
) => {
  let score = 0;
  const { timeSlots, dayOfWeek } = timeBasedPreferences;
  const currentTimeSlot = currentContext.timeSlot;
  const currentDayType = currentContext.dayOfWeek;

  // Time slot preferences
  if (timeSlots[currentTimeSlot] && timeSlots[currentTimeSlot].count > 0) {
    const dishCategory = dish.category?.name || "Other";
    const cuisine = dish.cuisine || "Other";

    const categoryCount =
      timeSlots[currentTimeSlot].categories[dishCategory] || 0;
    const cuisineCount = timeSlots[currentTimeSlot].cuisines[cuisine] || 0;
    const totalTimeSlotItems = Object.values(
      timeSlots[currentTimeSlot].categories
    ).reduce((sum, count) => sum + count, 0);

    if (totalTimeSlotItems > 0) {
      score += (categoryCount / totalTimeSlotItems) * 0.6;
      score += (cuisineCount / totalTimeSlotItems) * 0.4;
    }
  }

  // Day of week preferences
  if (dayOfWeek[currentDayType] && dayOfWeek[currentDayType].count > 0) {
    const dishCategory = dish.category?.name || "Other";
    const categoryCount =
      dayOfWeek[currentDayType].categories[dishCategory] || 0;
    const totalDayTypeItems = Object.values(
      dayOfWeek[currentDayType].categories
    ).reduce((sum, count) => sum + count, 0);

    if (totalDayTypeItems > 0) {
      score += (categoryCount / totalDayTypeItems) * 0.3;
    }
  }

  return Math.min(score, 1);
};

/**
 * Calculate seasonal score
 * @param {Object} dish - Dish object
 * @param {Object} seasonalPreferences - Seasonal preferences
 * @param {Object} currentContext - Current context
 * @returns {number} - Seasonal score (0-1)
 */
const calculateSeasonalScore = (dish, seasonalPreferences, currentContext) => {
  let score = 0;
  const currentSeason = currentContext.season;

  if (
    seasonalPreferences[currentSeason] &&
    seasonalPreferences[currentSeason].count > 0
  ) {
    const dishCategory = dish.category?.name || "Other";
    const cuisine = dish.cuisine || "Other";

    const categoryCount =
      seasonalPreferences[currentSeason].categories[dishCategory] || 0;
    const cuisineCount =
      seasonalPreferences[currentSeason].cuisines[cuisine] || 0;
    const totalSeasonItems = Object.values(
      seasonalPreferences[currentSeason].categories
    ).reduce((sum, count) => sum + count, 0);

    if (totalSeasonItems > 0) {
      score += (categoryCount / totalSeasonItems) * 0.7;
      score += (cuisineCount / totalSeasonItems) * 0.3;
    }
  }

  // Boost for seasonal dishes during their season
  if (dish.isSeasonal) {
    score += 0.2;
  }

  return Math.min(score, 1);
};

/**
 * Calculate contextual score based on user patterns
 * @param {Object} dish - Dish object
 * @param {Object} contextualPatterns - Contextual patterns
 * @param {Object} currentContext - Current context
 * @returns {number} - Contextual score (0-1)
 */
const calculateContextualScore = (dish, contextualPatterns, currentContext) => {
  let score = 0;

  // Special dish affinity
  if (dish.isSpecial && contextualPatterns.specialDishAffinity > 20) {
    score += (contextualPatterns.specialDishAffinity / 100) * 0.3;
  }

  // Seasonal dish affinity
  if (dish.isSeasonal && contextualPatterns.seasonalDishAffinity > 15) {
    score += (contextualPatterns.seasonalDishAffinity / 100) * 0.2;
  }

  // Price consideration based on time (higher prices for dinner)
  const price = dish.price;
  if (currentContext.isDinnerTime && price > 300) {
    score += 0.1; // Slight boost for premium dishes at dinner
  } else if (currentContext.isLunchTime && price < 250) {
    score += 0.1; // Slight boost for affordable dishes at lunch
  }

  return Math.min(score, 1);
};

/**
 * Calculate query relevance score
 * @param {Object} dish - Dish object
 * @param {string} userQuery - User's query
 * @returns {number} - Query relevance score (0-1)
 */
const calculateQueryRelevanceScore = (dish, userQuery) => {
  if (!userQuery || userQuery.length < 3) return 0;

  const query = userQuery.toLowerCase();
  const dishText = `${dish.name} ${dish.description || ""} ${
    dish.cuisine || ""
  } ${(dish.tags || []).join(" ")}`.toLowerCase();

  let score = 0;

  // Exact name match
  if (dish.name.toLowerCase().includes(query)) {
    score += 0.5;
  }

  // Description match
  if (dish.description && dish.description.toLowerCase().includes(query)) {
    score += 0.3;
  }

  // Cuisine match
  if (dish.cuisine && dish.cuisine.toLowerCase().includes(query)) {
    score += 0.2;
  }

  // Tags match
  if (dish.tags && dish.tags.some((tag) => tag.toLowerCase().includes(query))) {
    score += 0.2;
  }

  return Math.min(score, 1);
};

/**
 * Get enhanced dish recommendations for chat
 * @param {string} userId - User ID
 * @param {Array} availableDishes - Available dishes
 * @param {string} userQuery - User's query/message
 * @param {string} outletId - Outlet ID (optional)
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Array>} - Enhanced recommendations
 */
export const getEnhancedDishRecommendations = async (
  userId,
  availableDishes,
  userQuery = "",
  outletId = null,
  limit = 5
) => {
  try {
    // Get enhanced user preferences
    const enhancedPreferences = await getEnhancedUserPreferences(
      userId,
      outletId
    );

    // Get current context
    const currentContext = getCurrentContext();

    // Calculate enhanced scores for all dishes
    const scoredDishes = availableDishes.map((dish) => ({
      ...dish,
      enhancedScore: calculateEnhancedPersonalizationScore(
        dish,
        enhancedPreferences,
        currentContext,
        userQuery
      ),
      recommendationReason: generateRecommendationReason(
        dish,
        enhancedPreferences,
        currentContext,
        userQuery
      ),
    }));

    // Sort by enhanced score and filter
    const recommendations = scoredDishes
      .filter((dish) => dish.enhancedScore > 0.05) // Minimum threshold
      .sort((a, b) => b.enhancedScore - a.enhancedScore)
      .slice(0, limit);

    // If not enough personalized recommendations, add popular/trending dishes
    if (recommendations.length < limit) {
      const remaining = limit - recommendations.length;
      const fallbackDishes = availableDishes
        .filter(
          (dish) =>
            !recommendations.find(
              (rec) => rec._id.toString() === dish._id.toString()
            )
        )
        .sort((a, b) => {
          // Sort by ratings, then by special/seasonal status
          const aScore =
            (a.ratings?.average || 0) +
            (a.isSpecial ? 0.5 : 0) +
            (a.isSeasonal ? 0.3 : 0);
          const bScore =
            (b.ratings?.average || 0) +
            (b.isSpecial ? 0.5 : 0) +
            (b.isSeasonal ? 0.3 : 0);
          return bScore - aScore;
        })
        .slice(0, remaining)
        .map((dish) => ({
          ...dish,
          enhancedScore: 0.1,
          recommendationReason: dish.isSpecial
            ? "Chef's Special"
            : dish.isSeasonal
            ? "Seasonal Favorite"
            : "Popular Choice",
        }));

      recommendations.push(...fallbackDishes);
    }

    return recommendations;
  } catch (error) {
    console.error("Error getting enhanced dish recommendations:", error);
    // Fallback to basic recommendations
    return availableDishes.slice(0, limit).map((dish) => ({
      ...dish,
      enhancedScore: 0,
      recommendationReason: "Available Now",
    }));
  }
};

/**
 * Generate recommendation reason for a dish
 * @param {Object} dish - Dish object
 * @param {Object} enhancedPreferences - Enhanced user preferences
 * @param {Object} currentContext - Current context
 * @param {string} userQuery - User's query
 * @returns {string} - Recommendation reason
 */
const generateRecommendationReason = (
  dish,
  enhancedPreferences,
  currentContext,
  userQuery
) => {
  const reasons = [];
  const {
    dishFrequency,
    timeBasedPreferences,
    seasonalPreferences,
    contextualPatterns,
  } = enhancedPreferences;

  // Check if previously ordered
  const dishId = dish._id.toString();
  if (dishFrequency[dishId]) {
    const frequency = dishFrequency[dishId];
    if (frequency > 3) {
      reasons.push("One of your favorites");
    } else {
      reasons.push("You've enjoyed this before");
    }
  }

  // Time-based reasons
  const currentTimeSlot = currentContext.timeSlot;
  if (timeBasedPreferences.timeSlots[currentTimeSlot]?.count > 0) {
    const dishCategory = dish.category?.name || "Other";
    const categoryCount =
      timeBasedPreferences.timeSlots[currentTimeSlot].categories[
        dishCategory
      ] || 0;
    if (categoryCount > 0) {
      reasons.push(`Perfect for ${currentTimeSlot}`);
    }
  }

  // Seasonal reasons
  if (dish.isSeasonal) {
    reasons.push("Seasonal special");
  }

  // Special dish reasons
  if (dish.isSpecial) {
    reasons.push("Chef's recommendation");
  }

  // Query-based reasons
  if (userQuery && userQuery.length > 2) {
    const query = userQuery.toLowerCase();
    if (dish.name.toLowerCase().includes(query)) {
      reasons.push("Matches your request");
    } else if (dish.cuisine && dish.cuisine.toLowerCase().includes(query)) {
      reasons.push(`Great ${dish.cuisine} option`);
    }
  }

  // Category preference reasons
  const dishCategory = dish.category?.name || dish.category;
  if (
    dishCategory &&
    enhancedPreferences.preferences.favoriteCategories.length > 0
  ) {
    const categoryMatch =
      enhancedPreferences.preferences.favoriteCategories.find(
        (cat) => cat.category === dishCategory
      );
    if (categoryMatch && categoryMatch.percentage > 30) {
      reasons.push(`You love ${dishCategory.toLowerCase()}`);
    }
  }

  // Default reason
  if (reasons.length === 0) {
    if (dish.ratings?.average > 4) {
      reasons.push("Highly rated");
    } else {
      reasons.push("Recommended for you");
    }
  }

  return reasons[0]; // Return the most relevant reason
};
